name: System Tests

on:
  pull_request_target:
    types: [opened, synchronize, reopened]
    branches: [main, master]
  push:
    branches: [main, master]
  workflow_dispatch:

jobs:
  system-tests:
    name: Run System Tests
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [windows-latest, ubuntu-latest, macos-latest]
      max-parallel: 1
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.24.2'
          check-latest: false

      - name: Install dependencies
        run: go mod download

      # ───────────────────────────────────────── Windows only ─────────────────────────────────────────
      - name: Install git on Windows
        if: runner.os == 'Windows'
        shell: pwsh
        run: |
          if (-not (Get-Command git -ErrorAction SilentlyContinue)) {
            choco upgrade chocolatey -y
            choco install git -y --ignore-dependencies
          }

      # ───────────────────────────────────────── Linux only ───────────────────────────────────────────
      - name: Install clipboard utility on Linux
        if: runner.os == 'Linux'
        run: |
          sudo apt-get update
          sudo apt-get install -y xclip

      # ───────────────────────────────────────── macOS only ────────────────────────────────────────────
      - name: Install required tools on macOS
        if: runner.os == 'macOS'
        run: |
          # Install gawk (GNU awk) which is required by qs
          brew install gawk
          # Install jq if not already available
          brew install jq

      - name: Configure git identity
        run: |
          git config --global user.name "GitHub Actions Bot"
          git config --global user.email "<EMAIL>"

      - name: Install GitHub CLI (Windows)
        if: runner.os == 'Windows'
        shell: pwsh
        run: |
          choco install gh

      # ─────────────────────────────── Credential sanity checks ───────────────────────────────────────
      - name: Debug GitHub credentials (Linux/macOS)
        if: runner.os != 'Windows'
        env:
          UPSTREAM_GH_TOKEN: ${{ secrets.UPSTREAM_GH_TOKEN }}
          FORK_GH_TOKEN: ${{ secrets.FORK_GH_TOKEN }}
          UPSTREAM_GH_ACCOUNT: ${{ secrets.UPSTREAM_GH_ACCOUNT }}
          FORK_GH_ACCOUNT: ${{ secrets.FORK_GH_ACCOUNT }}
        shell: bash
        run: |
          echo "UPSTREAM_GH_ACCOUNT=$UPSTREAM_GH_ACCOUNT"
          echo "FORK_GH_ACCOUNT=$FORK_GH_ACCOUNT"
          echo "UPSTREAM_GH_TOKEN length=${#UPSTREAM_GH_TOKEN}"
          echo "FORK_GH_TOKEN length=${#FORK_GH_TOKEN}"

      - name: Debug GitHub credentials (Windows)
        if: runner.os == 'Windows'
        env:
          UPSTREAM_GH_TOKEN: ${{ secrets.UPSTREAM_GH_TOKEN }}
          FORK_GH_TOKEN:    ${{ secrets.FORK_GH_TOKEN }}
          UPSTREAM_GH_ACCOUNT: ${{ vars.UPSTREAM_GH_ACCOUNT }}
          FORK_GH_ACCOUNT:    ${{ vars.FORK_GH_ACCOUNT }}
        shell: pwsh
        run: |
          Write-Host "UPSTREAM_GH_ACCOUNT=$env:UPSTREAM_GH_ACCOUNT"
          Write-Host "FORK_GH_ACCOUNT=$env:FORK_GH_ACCOUNT"
          Write-Host "UPSTREAM_GH_TOKEN length=$($env:UPSTREAM_GH_TOKEN.Length)"
          Write-Host "FORK_GH_TOKEN length=$($env:FORK_GH_TOKEN.Length)"

      - name: Check GitHub credentials (Linux/macOS)
        if: runner.os != 'Windows'
        env:
          UPSTREAM_GH_TOKEN: ${{ secrets.UPSTREAM_GH_TOKEN }}
          FORK_GH_TOKEN: ${{ secrets.FORK_GH_TOKEN }}
          UPSTREAM_GH_ACCOUNT: ${{ vars.UPSTREAM_GH_ACCOUNT }}
          FORK_GH_ACCOUNT: ${{ vars.FORK_GH_ACCOUNT }}
        shell: bash
        run: |
          if [ -z "$UPSTREAM_GH_TOKEN" ] || [ -z "$FORK_GH_TOKEN" ] || [ -z "$UPSTREAM_GH_ACCOUNT" ] || [ -z "$FORK_GH_ACCOUNT" ]; then
            echo "Missing GitHub credentials"
            exit 1
          fi

      - name: Check GitHub credentials (Windows)
        if: runner.os == 'Windows'
        env:
          UPSTREAM_GH_TOKEN: ${{ secrets.UPSTREAM_GH_TOKEN }}
          FORK_GH_TOKEN: ${{ secrets.FORK_GH_TOKEN }}
          UPSTREAM_GH_ACCOUNT: ${{ vars.UPSTREAM_GH_ACCOUNT }}
          FORK_GH_ACCOUNT: ${{ vars.FORK_GH_ACCOUNT }}
        shell: pwsh
        run: |
          if (-not $env:UPSTREAM_GH_TOKEN -or -not $env:FORK_GH_TOKEN -or -not $env:UPSTREAM_GH_ACCOUNT -or -not $env:FORK_GH_ACCOUNT) {
            Write-Host "Missing GitHub credentials"
            exit 1
          }

      # ─────────────────────────────────────────── Run tests ──────────────────────────────────────────
      - name: Run system tests
        env:
          UPSTREAM_GH_TOKEN: ${{ secrets.UPSTREAM_GH_TOKEN }}
          FORK_GH_TOKEN: ${{ secrets.FORK_GH_TOKEN }}
          UPSTREAM_GH_ACCOUNT: ${{ vars.UPSTREAM_GH_ACCOUNT }}
          FORK_GH_ACCOUNT: ${{ vars.FORK_GH_ACCOUNT }}
          JIRA_API_TOKEN: ${{ secrets.JIRA_API_TOKEN }}
          JIRA_TICKET_URL: ${{ secrets.JIRA_TICKET_URL }}
          JIRA_EMAIL: ${{ secrets.JIRA_EMAIL }}
          GH_TIMEOUT_MS: ${{ vars.GH_TIMEOUT_MS }}
          QS_RETRY_DELAY_MS: ${{ vars.QS_RETRY_DELAY_MS }}
          QS_MAX_RETRY_DELAY_MS: ${{ vars.QS_MAX_RETRY_DELAY_MS }}
          QS_MAX_RETRIES: ${{ vars.QS_MAX_RETRIES }}
          QS_SKIP_QS_VERSION_CHECK: "true"
        run: |
          go test -v sys_test.go
