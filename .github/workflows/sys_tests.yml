name: System Tests

on:
  pull_request_target:
    types: [opened, synchronize, reopened]
    branches: [main, master]
  workflow_dispatch:
    inputs:
      platform:
        description: 'Platform to run tests on'
        required: true
        type: choice
        options:
          - all
          - windows-latest
          - ubuntu-latest
          - macos-latest
        default: all
jobs:
  system-tests-on-all-platforms:
    name: Run System Tests (all platforms)
    if: github.event_name != 'workflow_dispatch' || github.event.inputs.platform == 'all'
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: ['windows-latest', 'ubuntu-latest', 'macos-latest']
      max-parallel: 1
    steps:
      - name: Check prerequisites
        uses: ./.github/workflows/sys_tests_steps/check_prerequisites.yml

      - name: Run system tests
        uses: ./.github/workflows/sys_tests_steps/run_sys_tests.yml

  system-tests-on-specific-platform:
    name: Run System Tests (${{ github.event.inputs.platform }})
    runs-on: ${{ github.event.inputs.platform }}
    if: github.event_name == 'workflow_dispatch' || github.event.inputs.platform != 'all'
    steps:
      - name: Check prerequisites
        uses: ./.github/workflows/sys_tests_steps/check_prerequisites.yml

      - name: Run system tests
        uses: ./.github/workflows/sys_tests_steps/run_sys_tests.yml
